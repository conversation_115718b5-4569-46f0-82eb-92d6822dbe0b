<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>GovPSS</title>
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">

    <style>
        :root {
            --png-red: #CE1126;
            --png-black: #000000;
            --png-gold: #FCD116;
            --dark-green: #006400;
        }

        body {
            font-family: 'helvetica', sans-serif;
            background-color: #f8f9fa;
        }

        .letter-container {
            max-width: 210mm;
            margin: 20px auto;
            padding: 20px 25mm;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            min-height: 297mm;
            position: relative;
        }

        .org-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .org-logo {
            max-width: 30mm;
            margin: 5mm auto;
        }

        .org-name {
            font-size: 16pt;
            font-weight: bold;
            text-transform: uppercase;
            margin: 22pt 0;
        }

        .dept-name {
            font-size: 14pt;
            font-weight: bold;
            text-transform: uppercase;
            color: var(--dark-green);
            margin: 22pt 0;
        }

        .header-line {
            border-top: 0.5mm solid black;
            margin: 5mm 0;
        }

        .letter-content {
            font-size: 11pt;
            line-height: 1.5;
        }

        .letter-date {
            text-align: right;
            margin: 10pt 0;
        }

        .letter-file-code {
            text-align: right;
            margin: 2pt 0;
        }

        .letter-address {
            margin: 10pt 0;
        }

        .letter-subject {
            font-weight: bold;
            margin: 5pt 0;
        }

        .letter-body {
            text-align: justify;
            margin: 5pt 0;
        }

        .letter-footer {
            position: absolute;
            bottom: 25mm;
            left: 25mm;
            right: 25mm;
        }

        .footer-line {
            border-top: 0.2mm solid var(--dark-green);
            margin-bottom: 10pt;
        }

        .contact-info {
            text-align: center;
            color: #444;
            font-size: 11pt;
        }

        .contact-details {
            display: flex;
            justify-content: space-between;
            margin: 4pt 0;
        }

        .generation-info {
            text-align: center;
            font-style: italic;
            font-size: 7pt;
            position: absolute;
            bottom: 5mm;
            left: 0;
            right: 0;
        }

        .qr-code {
            position: absolute;
            bottom: 43mm;
            right: 25mm;
            width: 25mm;
            height: 25mm;
        }

        .signature-section {
            margin-top: 15mm;
        }

        .signature-position {
            font-weight: bold;
            margin-bottom: 5pt;
        }

        .btn-primary {
            background-color: var(--png-red);
            border-color: var(--png-red);
        }

        .btn-primary:hover {
            background-color: #a00d1d;
            border-color: #a00d1d;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" height="40" class="me-2">
                <span>GovPSS</span>
            </a>
        </div>
    </nav>

    <!-- Letter Preview Container -->
    <div class="letter-container">
        <!-- Organization Header -->
        <div class="org-header py-1">
            <img src="<?= isset($orgLogo) ? $orgLogo : base_url('public/assets/system_img/system-logo.png') ?>" alt="Organization Logo" class="org-logo">
            <div class="org-name"><?= isset($orgName) ? strtoupper($orgName) : 'ORGANIZATION NAME' ?></div>
            <div class="dept-name">DIVISION OF HUMAN RESOURCE MANAGEMENT</div>
        </div>
        
        <div class="header-line"></div>

        <!-- Letter Content -->
        <div class="letter-content">
            <!-- Date and File Code -->
            <div class="letter-date">
                <?= isset($letter['confirmed_at']) ? date('d F Y', strtotime($letter['confirmed_at'])) : date('d F Y') ?>
            </div>
            <div class="letter-file-code">
                File Code: <?= isset($letter['letter_file_code']) ? $letter['letter_file_code'] : 'N/A' ?>
            </div>

            <!-- Address -->
            <div class="letter-address">
                <?= isset($letter['address_to']) ? nl2br(esc($letter['address_to'])) : '' ?>
            </div>

            <!-- Salutation -->
            <div>Dear Sir/Madam,</div>

            <!-- Subject -->
            <div class="letter-subject">
                <?= isset($letter['subject']) ? esc($letter['subject']) : '' ?>
            </div>

            <!-- Letter Body -->
            <div class="letter-body">
                <?= isset($letter['content']) ? nl2br(esc($letter['content'])) : '' ?>
            </div>

            <!-- Signature Section -->
            <div class="signature-section">
                <div class="signature-position" style="margin-bottom: 20px;">Yours faithfully,</div>

                <div class="signature-container d-flex justify-content-between">
                    <img src="<?= isset($signature) ? base_url($signature) : '' ?>" alt="Signature" class="signature-image" width="200" height="100">
                    
                    <img src="<?= isset($stamp_filepath) ? base_url($stamp_filepath) : '' ?>" alt="Stamp" class="stamp-image" width="170" height="170" style="position: absolute; left: 75mm; z-index: 1;">
                    <img src="<?= isset($approved_stamp_filepath) ? base_url($approved_stamp_filepath) : '' ?>" alt="Approved Stamp" class="approved-stamp-image" width="150" height="150" style="position: absolute; left: 130mm; z-index: 2;">
                 
                </div>

                <div class="signature-details">
                    <div class="signature-name">
                        <?= isset($signature_name) ? esc($signature_name) : '' ?>
                    </div>
                    <div class="signature-title">
                        <?= isset($signature_position) ? esc($signature_position) : '' ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="letter-footer">
            <div class="footer-line"></div>
            <div class="contact-info">
                <div class="contact-details">
                    <span>Phone: <?= isset($orgPhone) ? $orgPhone : '+675 XXX XXXX' ?></span>
                    <span>Email: <?= isset($orgEmail) ? $orgEmail : '<EMAIL>' ?></span>
                </div>
                <div><?= isset($orgAddress) ? $orgAddress : 'Organization Address, Papua New Guinea' ?></div>
            </div>
        </div>

        <!-- QR Code Placeholder -->
        <div class="qr-code">
            <!-- QR code will be added here -->
        </div>

        <!-- Generation Info -->
        <div class="generation-info">
            Generated by GovPSS on <?= date('d/m/Y H:i:s') ?>
        </div>
    </div>

    <!-- Process Form -->
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-4">
                        <form action="<?= base_url('process_letter/' . $letter['unique_code']) ?>" method="POST" id="letterForm">
                            <?= csrf_field() ?>
                            <?php
                            // Extract first email if multiple emails are present
                            $firstEmail = '';
                            if (!empty($orgEmail)) {
                                $emails = explode(',', $orgEmail);
                                $firstEmail = trim($emails[0]);
                            }
                            ?>
                            <input type="hidden" name="email" value="<?= esc($firstEmail) ?>">
                            
                            <div class="mb-4">
                                <label for="remarks" class="form-label">Remarks <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" required 
                                    placeholder="Enter your remarks/comments about the letter"></textarea>
                                <div class="invalid-feedback">Please provide your remarks.</div>
                            </div>

                            <!-- Hidden status field -->
                            <input type="hidden" name="status" id="letterStatus" value="">

                            <div class="d-flex gap-3">
                                <button type="button" class="btn btn-success flex-grow-1" onclick="handleSubmit('approved')">
                                    <i class="fas fa-check me-2"></i>Approve
                                </button>
                                <button type="button" class="btn btn-danger flex-grow-1" onclick="handleSubmit('rejected')">
                                    <i class="fas fa-times me-2"></i>Reject
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

    <!-- Form Handling Script -->
    <script>
    function handleSubmit(status) {
        const remarks = document.getElementById('remarks').value.trim();
        if (!remarks) {
            Swal.fire({
                title: 'Error!',
                text: 'Please provide remarks before ' + (status === 'approved' ? 'approving' : 'rejecting') + ' the letter.',
                icon: 'error',
                confirmButtonColor: '#CE1126'
            });
            return;
        }
        
        // Set the status value
        document.getElementById('letterStatus').value = status;
        
        // Show confirmation dialog
        Swal.fire({
            title: 'Confirm ' + (status === 'approved' ? 'Approval' : 'Rejection'),
            text: 'Are you sure you want to ' + (status === 'approved' ? 'approve' : 'reject') + ' this letter?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: status === 'approved' ? '#28a745' : '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Yes, ' + (status === 'approved' ? 'approve' : 'reject') + ' it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById('letterForm').submit();
            }
        });
    }
    </script>

    <!-- SweetAlert Messages -->
    <script>
        <?php if (session()->getFlashdata('success')): ?>
            Swal.fire({
                title: 'Success!',
                text: '<?= session()->getFlashdata('success') ?>',
                icon: 'success',
                confirmButtonColor: '#CE1126'
            });
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            Swal.fire({
                title: 'Error!',
                text: '<?= session()->getFlashdata('error') ?>',
                icon: 'error',
                confirmButtonColor: '#CE1126'
            });
        <?php endif; ?>
    </script>
</body>
</html>