<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<!-- Employee Info Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3"
                                 style="width: 60px; height: 60px; font-size: 24px;">
                                <?= strtoupper(substr($employee['fname'], 0, 1) . substr($employee['lname'], 0, 1)) ?>
                            </div>
                            <div>
                                <h4 class="mb-1"><?= esc($employee['fname'] . ' ' . $employee['lname']) ?></h4>
                                <div class="text-muted">
                                    <span class="badge bg-secondary me-2">File No: <?= esc($employee['fileno']) ?></span>
                                    <?php if (!empty($employee['position_name'])): ?>
                                        <span class="badge bg-info me-2"><?= esc($employee['position_name']) ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($employee['group_name'])): ?>
                                        <span class="badge bg-success"><?= esc($employee['group_name']) ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="<?= base_url('leave') ?>" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i>Back to Employees
                        </a>
                        <a href="<?= base_url('leave/create/' . $employee['emp_id']) ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Add Leave
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leave Records -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Leave Records
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="leavesTable" class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Leave Type</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Duration</th>
                                <th>Pay Status</th>
                                <th>Remarks</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($leaves)): ?>
                                <?php foreach ($leaves as $leave): ?>
                                    <tr>
                                        <td>
                                            <?php
                                            // Find the leave type name from the code
                                            $leave_type_name = $leave['leave_type']; // Default to code if not found
                                            if (!empty($leave_types)) {
                                                foreach ($leave_types as $type) {
                                                    if ($type['code'] == $leave['leave_type']) {
                                                        $leave_type_name = $type['name'];
                                                        break;
                                                    }
                                                }
                                            }
                                            ?>
                                            <span class="badge bg-info">
                                                <?= esc($leave_type_name) ?>
                                            </span>
                                            <small class="text-muted d-block mt-1">
                                                Code: <?= esc($leave['leave_type']) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar me-1 text-success"></i>
                                            <?= date('M d, Y', strtotime($leave['date_from'])) ?>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar me-1 text-danger"></i>
                                            <?= date('M d, Y', strtotime($leave['date_to'])) ?>
                                        </td>
                                        <td>
                                            <?php
                                            $start = new DateTime($leave['date_from']);
                                            $end = new DateTime($leave['date_to']);
                                            $interval = $start->diff($end);
                                            $days = $interval->days + 1; // Include both start and end dates
                                            ?>
                                            <span class="badge bg-warning text-dark">
                                                <?= $days ?> day<?= $days > 1 ? 's' : '' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($leave['is_paid'] == 1): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle me-1"></i>Paid
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle me-1"></i>Unpaid
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($leave['remarks'])): ?>
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                                      title="<?= esc($leave['remarks']) ?>">
                                                    <?= esc($leave['remarks']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-minus"></i>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?= date('M d, Y', strtotime($leave['created_at'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?= base_url('leave/edit/' . $leave['id']) ?>"
                                                   class="btn btn-outline-primary btn-sm"
                                                   title="Edit Leave">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button"
                                                        class="btn btn-outline-danger btn-sm"
                                                        onclick="confirmDelete(<?= $leave['id'] ?>)"
                                                        title="Delete Leave">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-times fa-2x mb-3 d-block"></i>
                                        <p class="mb-0">No leave records found</p>
                                        <small>Click "Add Leave" to create the first leave record</small>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables Initialization -->
<script>
$(document).ready(function() {
    $('#leavesTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[1, 'desc']], // Sort by start date descending
        columnDefs: [
            {
                targets: [7], // Actions column
                orderable: false,
                searchable: false
            }
        ],
        language: {
            search: "Search leave records:",
            lengthMenu: "Show _MENU_ records per page",
            info: "Showing _START_ to _END_ of _TOTAL_ leave records",
            infoEmpty: "No leave records found",
            infoFiltered: "(filtered from _MAX_ total records)",
            emptyTable: "No leave records available",
            zeroRecords: "No matching leave records found"
        }
    });
});

function confirmDelete(leaveId) {
    Swal.fire({
        title: 'Delete Leave Record?',
        text: 'This action cannot be undone. The leave record will be permanently deleted.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#CE1126',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Yes, Delete',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '<?= base_url('leave/delete/') ?>' + leaveId;
        }
    });
}
</script>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.badge {
    font-size: 0.75rem;
}

.btn-sm {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

.btn-group .btn {
    border-radius: 6px;
    margin: 0 1px;
}

.text-truncate {
    max-width: 200px;
}

/* DataTables custom styling */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--png-red);
    box-shadow: 0 0 0 0.25rem rgba(206, 17, 38, 0.25);
}
</style>

<?= $this->endSection() ?>
