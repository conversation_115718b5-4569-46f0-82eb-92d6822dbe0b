<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class Auth implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Get the current URI path
        $currentPath = $request->getUri()->getPath();

        // Allow public access to letter view and process routes
        if (strpos($currentPath, 'view_letter/') !== false || strpos($currentPath, 'process_letter/') !== false) {
            return;
        }

        // Allow public access to Dakoii index and login only
        if ($currentPath === 'dakoii' || $currentPath === 'dlogin') {
            return;
        }

        // Check if any arguments were passed
        if (!empty($arguments)) {
            // Check for employee authentication
            if ($arguments[0] === 'employee') {
                if (!session()->get('isEmployeeLoggedIn')) {
                    return redirect()->to('/')->with('error', 'Please login to access your portal');
                }
                return;
            }

            // Check for admin role-based access
            if (!session()->get('logged_in') || session()->get('role') !== $arguments[0]) {
                return redirect()->back()->with('error', 'Access denied');
            }
            return;
        }

        // Default admin authentication check
        if (!session()->get('logged_in')) {
            // If trying to access Dakoii protected routes
            if (strpos($currentPath, 'dakoii/') === 0 || $currentPath === 'ddash') {
                return redirect()->to(base_url('dakoii'))->with('error', 'Please login to access Dakoii dashboard');
            }
            return redirect()->to(base_url())->with('error', 'Please login to continue');
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No after-filter needed
    }
}
