<?php namespace App\Models;

use CodeIgniter\Model;

class employeesModel extends Model
{
    protected $table = 'employees';
    protected $primaryKey = 'emp_id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'org_id',
        'fileno',
        'password',
        'fname',
        'lname',
        'gender',
        'dobirth',
        'commence_date',
        'id_photo',
        'phone',
        'primary_email',
        'other_contacts',
        'status',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Callbacks
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (!isset($data['data']['password'])) {
            return $data;
        }

        $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        return $data;
    }

    // Custom Methods
    public function findByFileNo($fileno)
    {
        return $this->where('fileno', $fileno)
                    ->first();
    }

    public function getActiveEmployees()
    {
        return $this->where('status', 'active')
                    ->findAll();
    }

    /**
     * Get employees due for Recreation Leave (RL) this year
     *
     * @param int $org_id Organization ID
     * @return array Array of employees due for leave
     */
    public function getEmployeesDueForLeave($org_id = null)
    {
        $currentYear = date('Y');
        $employeeLeavesModel = new \App\Models\EmployeeLeavesModel();

        // Get all active employees
        $query = $this->where('status', 'active');
        if ($org_id !== null) {
            $query->where('org_id', $org_id);
        }
        $employees = $query->findAll();

        $employeesDue = [];

        foreach ($employees as $employee) {
            $isDue = false;
            $dueYear = null;
            $lastLeaveDate = null;

            // Check if employee has any RL leave records
            $latestRLLeave = $employeeLeavesModel->getLatestRLLeave($employee['emp_id']);

            if ($latestRLLeave) {
                // Employee has RL leave record - calculate 2 years from end date
                $leaveEndDate = new \DateTime($latestRLLeave['date_to']);
                $dueDate = clone $leaveEndDate;
                $dueDate->modify('+2 years');
                $dueYear = (int)$dueDate->format('Y');
                $lastLeaveDate = $latestRLLeave['date_to'];
            } else {
                // No RL leave record - calculate 2 years from commencement date
                if ($employee['commence_date']) {
                    $commenceDate = new \DateTime($employee['commence_date']);
                    $dueDate = clone $commenceDate;
                    $dueDate->modify('+2 years');
                    $dueYear = (int)$dueDate->format('Y');
                }
            }

            // Check if employee is due for leave this year or earlier
            if ($dueYear && $dueYear <= $currentYear) {
                $employee['due_year'] = $dueYear;
                $employee['last_leave_date'] = $lastLeaveDate;
                $employee['years_overdue'] = $currentYear - $dueYear;
                $employeesDue[] = $employee;
            }
        }

        return $employeesDue;
    }

    /**
     * Count employees due for Recreation Leave (RL) this year
     *
     * @param int $org_id Organization ID
     * @return int Count of employees due for leave
     */
    public function countEmployeesDueForLeave($org_id = null)
    {
        return count($this->getEmployeesDueForLeave($org_id));
    }

    /**
     * Get employees due for Recreation Leave (RL) next year
     *
     * @param int $org_id Organization ID
     * @return array Array of employees due for leave next year
     */
    public function getEmployeesDueForLeaveNextYear($org_id = null)
    {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $employeeLeavesModel = new \App\Models\EmployeeLeavesModel();

        // Get all active employees
        $query = $this->where('status', 'active');
        if ($org_id !== null) {
            $query->where('org_id', $org_id);
        }
        $employees = $query->findAll();

        $employeesDue = [];

        foreach ($employees as $employee) {
            $dueYear = null;
            $lastLeaveDate = null;

            // Check if employee has any RL leave records
            $latestRLLeave = $employeeLeavesModel->getLatestRLLeave($employee['emp_id']);

            if ($latestRLLeave) {
                // Employee has RL leave record - calculate 2 years from end date
                $leaveEndDate = new \DateTime($latestRLLeave['date_to']);
                $dueDate = clone $leaveEndDate;
                $dueDate->modify('+2 years');
                $dueYear = (int)$dueDate->format('Y');
                $lastLeaveDate = $latestRLLeave['date_to'];
            } else {
                // No RL leave record - calculate 2 years from commencement date
                if ($employee['commence_date']) {
                    $commenceDate = new \DateTime($employee['commence_date']);
                    $dueDate = clone $commenceDate;
                    $dueDate->modify('+2 years');
                    $dueYear = (int)$dueDate->format('Y');
                }
            }

            // Check if employee is due for leave next year specifically
            if ($dueYear && $dueYear == $nextYear) {
                $employee['due_year'] = $dueYear;
                $employee['last_leave_date'] = $lastLeaveDate;
                $employee['years_until_due'] = $nextYear - $currentYear;
                $employeesDue[] = $employee;
            }
        }

        return $employeesDue;
    }

    /**
     * Count employees due for Recreation Leave (RL) next year
     *
     * @param int $org_id Organization ID
     * @return int Count of employees due for leave next year
     */
    public function countEmployeesDueForLeaveNextYear($org_id = null)
    {
        return count($this->getEmployeesDueForLeaveNextYear($org_id));
    }

    public function getEmployeeFullName($emp_id)
    {
        $employee = $this->find($emp_id);
        if ($employee) {
            return $employee['fname'] . ' ' . $employee['lname'];
        }
        return null;
    }

    /**
     * Get employee with position and group details
     *
     * @param string $field Field to search by (emp_id or fileno)
     * @param string $value Value to search for
     * @return array|null Employee data with position and group details
     */
    public function getEmployeeWithDetails($field, $value)
    {
        return $this->select('employees.*, positions.designation as position_name, positions.appointment_type, groupings.name as group_name')
            ->join('positions', 'positions.employee_id = employees.emp_id', 'left')
            ->join('groupings', 'groupings.id = positions.group_id', 'left')
            ->where('employees.' . $field, $value)
            ->first();
    }
}