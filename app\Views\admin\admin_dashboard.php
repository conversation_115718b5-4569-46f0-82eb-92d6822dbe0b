<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- Dashboard Content -->
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="card mb-4 border-start border-danger border-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Welcome back, <?= $user_name ?>!</h2>
                    <p class="text-muted mb-0">Here's what's happening in your system today.</p>
                </div>
                <div class="col-auto">
                    <span class="text-muted"><?= date('l, F j, Y') ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="row g-4 mb-4">
        <!-- Total Users Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Organization Users</p>
                            <h3 class="fw-bold mb-3"><?= $total_users ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 rounded p-3">
                            <i class="fas fa-users text-danger fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i>12%
                        </span>
                        <span class="text-muted small">From last month</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--png-red), #dc3545);"></div>
                </div>
            </div>
        </div>

        <!-- Total Employees Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Organization Employees</p>
                            <h3 class="fw-bold mb-3"><?= $total_employees ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 rounded p-3">
                            <i class="fas fa-user-tie text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-success me-2">
                            <i class="fas fa-arrow-up me-1"></i>8%
                        </span>
                        <span class="text-muted small">From last month</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, var(--png-gold), #ffc107);"></div>
                </div>
            </div>
        </div>

        <!-- Employees Due for Leave This Year Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Due for Leave (<?= date('Y') ?>)</p>
                            <h3 class="fw-bold mb-3"><?= $employees_due_leave ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 rounded p-3">
                            <i class="fas fa-calendar-times text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small">Recreation Leave (RL) due</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #ffc107, #fd7e14);"></div>
                </div>
            </div>
        </div>

        <!-- Employees Due for Leave Next Year Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Due for Leave (<?= date('Y') + 1 ?>)</p>
                            <h3 class="fw-bold mb-3"><?= $employees_due_leave_next_year ?></h3>
                        </div>
                        <div class="bg-info bg-opacity-10 rounded p-3">
                            <i class="fas fa-calendar-plus text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small">Recreation Leave (RL) next year</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #0dcaf0, #0d6efd);"></div>
                </div>
            </div>
        </div>

        <!-- Positions Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Positions</p>
                            <h3 class="fw-bold mb-3"><?= $total_positions ?></h3>
                        </div>
                        <div class="bg-dark bg-opacity-10 rounded p-3">
                            <i class="fas fa-briefcase text-dark fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small">Total active positions</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #212529, #495057);"></div>
                </div>
            </div>
        </div>

        <!-- Payslips Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Payslips</p>
                            <h3 class="fw-bold mb-3"><?= $total_payslips ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 rounded p-3">
                            <i class="fas fa-file-invoice-dollar text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small">Total generated payslips</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #198754, #20c997);"></div>
                </div>
            </div>
        </div>

        <!-- Letters Card -->
        <div class="col-12 col-sm-6 col-lg-3">
            <div class="card hover-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <p class="text-muted small mb-2">Letters</p>
                            <h3 class="fw-bold mb-3"><?= $total_letters ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 rounded p-3">
                            <i class="fas fa-envelope text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="text-muted small">Total letters</span>
                    </div>
                </div>
                <div class="card-footer p-0">
                    <div class="bg-gradient" style="height: 4px; background: linear-gradient(to right, #198754, #20c997);"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Section -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title mb-4">Quick Access</h5>
            <div class="row g-4">
                <!-- Employees -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('employees') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-user-tie text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Employees</span>
                        </div>
                    </a>
                </div>

                <!-- Manage Positions -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('groups') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-briefcase text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Manage Positions</span>
                        </div>
                    </a>
                </div>

                <!-- Manage Appointments -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('appointments') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-file-signature text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Manage Appointments</span>
                        </div>
                    </a>
                </div>

                <!-- Manage Payslips -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('payslips') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-receipt text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Manage Payslips</span>
                        </div>
                    </a>
                </div>

                <!-- Establishment Register -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('reports/establishment') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-clipboard-list text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Establishment Register</span>
                        </div>
                    </a>
                </div>

                <!-- Salary Analysis -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('reports/salary') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-chart-line text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Salary Analysis</span>
                        </div>
                    </a>
                </div>

                <!-- Org Settings -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('settings/organization') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-cog text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Org. Settings</span>
                        </div>
                    </a>
                </div>

                <!-- Letters Management -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('letters') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-envelope text-danger fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Letters Management</span>
                        </div>
                    </a>
                </div>

                <!-- Employees Due for Leave This Year -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('employees_due_leave') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-calendar-times text-warning fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Due for Leave (<?= date('Y') ?>)</span>
                        </div>
                    </a>
                </div>

                <!-- Employees Due for Leave Next Year -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <a href="<?= base_url('employees_due_leave_next_year') ?>" class="card text-decoration-none hover-card">
                        <div class="card-body d-flex align-items-center">
                            <i class="fas fa-calendar-plus text-info fs-4 me-3"></i>
                            <span class="text-dark fw-medium">Due for Leave (<?= date('Y') + 1 ?>)</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Letters Section -->
    <div class="card mt-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Recent Letters</h5>
            <a href="<?= base_url('letters') ?>" class="btn btn-link text-info text-decoration-none">View All</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Subject</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_letters as $letter): ?>
                        <tr>
                            <td class="px-4">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                        <i class="fas fa-envelope text-muted"></i>
                                    </div>
                                    <span class="fw-medium"><?= esc($letter['subject']) ?></span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info bg-opacity-10 text-info">
                                    <?= ucfirst($letter['letter_type']) ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $statusClasses = [
                                    'approved' => 'bg-success text-success',
                                    'rejected' => 'bg-danger text-danger'
                                ];
                                $statusClass = $statusClasses[$letter['confirmation_status']] ?? 'bg-warning text-warning';
                                ?>
                                <span class="badge bg-opacity-10 <?= $statusClass ?>">
                                    <?= ucfirst($letter['confirmation_status']) ?>
                                </span>
                            </td>
                            <td><?= date('M d, Y', strtotime($letter['created_at'])) ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Payslips Section -->
    <div class="card mt-4 mb-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Recent Payslips</h5>
            <a href="<?= base_url('payslips') ?>" class="btn btn-link text-success text-decoration-none">View All</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Payslip No.</th>
                            <th>Date</th>
                            <th>Period</th>
                            <th>File</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_payslips as $payslip): ?>
                        <tr>
                            <td class="px-4">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                        <i class="fas fa-file-invoice text-muted"></i>
                                    </div>
                                    <span class="fw-medium"><?= $payslip['pay_no'] ?></span>
                                </div>
                            </td>
                            <td><?= date('M d, Y', strtotime($payslip['pay_date'])) ?></td>
                            <td><?= date('F Y', strtotime($payslip['pay_date'])) ?></td>
                            <td>
                                <?php if ($payslip['file_path']): ?>
                                <a href="<?= base_url($payslip['file_path']) ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download me-1"></i>Download
                                </a>
                                <?php else: ?>
                                <span class="text-muted">No file</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Employees Section -->
    <div class="card mb-4">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Recent Organization Employees</h5>
            <a href="<?= base_url('employees') ?>" class="btn btn-link text-warning text-decoration-none">View All</a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Name</th>
                            <th>File No.</th>
                            <th>Gender</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_employees as $employee): ?>
                        <tr>
                            <td class="px-4">
                                <div class="d-flex align-items-center">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                                        <?php if ($employee['id_photo']): ?>
                                            <img src="<?= base_url($employee['id_photo']) ?>" alt="Profile" class="rounded-circle" style="width: 32px; height: 32px; object-fit: cover;">
                                        <?php else: ?>
                                            <i class="fas fa-user-tie text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                    <span class="fw-medium"><?= $employee['fname'] . ' ' . $employee['lname'] ?></span>
                                </div>
                            </td>
                            <td><?= $employee['fileno'] ?></td>
                            <td>
                                <span class="badge bg-info bg-opacity-10 text-info">
                                    <?= ucfirst($employee['gender']) ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge <?= $employee['status'] == 'active' ? 'bg-success' : 'bg-danger' ?> bg-opacity-10 <?= $employee['status'] == 'active' ? 'text-success' : 'text-danger' ?>">
                                    <?= ucfirst($employee['status']) ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>