<?php

namespace App\Models;

use CodeIgniter\Model;

class EmployeeLeavesModel extends Model
{
    protected $table = 'employee_leaves';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'org_id',
        'employee_id',
        'leave_type',
        'date_from',
        'date_to',
        'is_paid',
        'remarks',
        'created_by',
        'updated_by',
        'is_deleted',
        'deleted_at',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation rules based on the table schema
    protected $validationRules = [
        'org_id' => 'required|integer',
        'employee_id' => 'required|integer',
        'leave_type' => 'required|max_length[50]',
        'date_from' => 'required|valid_date',
        'date_to' => 'required|valid_date',
        'is_paid' => 'required|in_list[0,1]',
        'remarks' => 'permit_empty',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'integer' => 'Organization ID must be a valid integer'
        ],
        'employee_id' => [
            'required' => 'Employee ID is required',
            'integer' => 'Employee ID must be a valid integer'
        ],
        'leave_type' => [
            'required' => 'Leave type is required',
            'max_length' => 'Leave type cannot exceed 50 characters'
        ],
        'date_from' => [
            'required' => 'Start date is required',
            'valid_date' => 'Start date must be a valid date'
        ],
        'date_to' => [
            'required' => 'End date is required',
            'valid_date' => 'End date must be a valid date'
        ],
        'is_paid' => [
            'required' => 'Pay status is required',
            'in_list' => 'Pay status must be either paid (1) or unpaid (0)'
        ]
    ];

    protected $skipValidation = false;

    // Before insert callback to ensure default values
    protected $beforeInsert = ['setDefaultValues'];
    protected $beforeUpdate = ['setUpdatedValues'];

    protected function setDefaultValues(array $data)
    {
        if (!isset($data['data']['is_deleted'])) {
            $data['data']['is_deleted'] = false;
        }
        return $data;
    }

    protected function setUpdatedValues(array $data)
    {
        // Any additional logic for updates can be added here
        return $data;
    }

    // Database field types for reference
    protected $fieldTypes = [
        'id' => 'int',
        'org_id' => 'int',
        'employee_id' => 'int',
        'leave_type' => 'varchar',
        'date_from' => 'date',
        'date_to' => 'date',
        'is_paid' => 'tinyint',
        'remarks' => 'text',
        'created_by' => 'int',
        'updated_by' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'is_deleted' => 'boolean',
        'deleted_at' => 'datetime',
        'deleted_by' => 'int'
    ];

    /**
     * Get all active employee leaves (not deleted)
     *
     * @param int $org_id Organization ID
     * @return array Array of active employee leaves
     */
    public function getActiveLeaves($org_id = null)
    {
        $query = $this->where('is_deleted', false);

        if ($org_id !== null) {
            $query->where('org_id', $org_id);
        }

        return $query->orderBy('date_from', 'DESC')->findAll();
    }

    /**
     * Get the latest Recreation Leave (RL) record for an employee
     *
     * @param int $employee_id Employee ID
     * @return array|null Latest RL leave record or null if none found
     */
    public function getLatestRLLeave($employee_id)
    {
        return $this->where('employee_id', $employee_id)
                   ->where('leave_type', 'RL')
                   ->where('is_deleted', false)
                   ->orderBy('date_to', 'DESC')
                   ->first();
    }

    /**
     * Get employee leaves by employee ID
     *
     * @param int $employee_id Employee ID
     * @param bool $includeDeleted Whether to include deleted records
     * @return array Array of employee leaves
     */
    public function getLeavesByEmployee($employee_id, $includeDeleted = false)
    {
        $query = $this->where('employee_id', $employee_id);

        if (!$includeDeleted) {
            $query->where('is_deleted', false);
        }

        return $query->orderBy('date_from', 'DESC')->findAll();
    }

    /**
     * Get employee leaves by date range
     *
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @param int $org_id Organization ID
     * @return array Array of employee leaves within date range
     */
    public function getLeavesByDateRange($startDate, $endDate, $org_id = null)
    {
        $query = $this->where('is_deleted', false)
                     ->groupStart()
                         ->where('date_from >=', $startDate)
                         ->where('date_from <=', $endDate)
                     ->groupEnd()
                     ->orGroupStart()
                         ->where('date_to >=', $startDate)
                         ->where('date_to <=', $endDate)
                     ->groupEnd()
                     ->orGroupStart()
                         ->where('date_from <=', $startDate)
                         ->where('date_to >=', $endDate)
                     ->groupEnd();

        if ($org_id !== null) {
            $query->where('org_id', $org_id);
        }

        return $query->orderBy('date_from', 'ASC')->findAll();
    }

    /**
     * Get employee leaves by leave type
     *
     * @param string $leave_type Leave type
     * @param int $org_id Organization ID
     * @return array Array of employee leaves by type
     */
    public function getLeavesByType($leave_type, $org_id = null)
    {
        $query = $this->where('is_deleted', false)
                     ->where('leave_type', $leave_type);

        if ($org_id !== null) {
            $query->where('org_id', $org_id);
        }

        return $query->orderBy('date_from', 'DESC')->findAll();
    }

    /**
     * Soft delete an employee leave record
     *
     * @param int $id Leave record ID
     * @param int $deleted_by User ID who is deleting the record
     * @return bool Success status
     */
    public function softDelete($id, $deleted_by = null)
    {
        $data = [
            'is_deleted' => true,
            'deleted_at' => date('Y-m-d H:i:s')
        ];

        if ($deleted_by !== null) {
            $data['deleted_by'] = $deleted_by;
        }

        return $this->update($id, $data);
    }

    /**
     * Restore a soft deleted employee leave record
     *
     * @param int $id Leave record ID
     * @return bool Success status
     */
    public function restore($id)
    {
        $data = [
            'is_deleted' => false,
            'deleted_at' => null,
            'deleted_by' => null
        ];

        return $this->update($id, $data);
    }

    /**
     * Create a new employee leave record
     *
     * @param array $data Leave data
     * @return array Result with status and message
     */
    public function createLeave($data)
    {
        try {
            // Validate date range
            if (isset($data['date_from']) && isset($data['date_to'])) {
                if (strtotime($data['date_from']) > strtotime($data['date_to'])) {
                    return [
                        'status' => false,
                        'message' => 'Start date cannot be later than end date'
                    ];
                }
            }

            // Validate and insert the data
            if ($this->save($data)) {
                return [
                    'status' => true,
                    'message' => 'Employee leave created successfully',
                    'id' => $this->getInsertID()
                ];
            }

            return [
                'status' => false,
                'message' => 'Failed to create employee leave',
                'errors' => $this->errors()
            ];
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return [
                'status' => false,
                'message' => 'An error occurred while creating the employee leave'
            ];
        }
    }

    /**
     * Update an employee leave record
     *
     * @param int $id Leave record ID
     * @param array $data Updated leave data
     * @return array Result with status and message
     */
    public function updateLeave($id, $data)
    {
        try {
            // Check if record exists and is not deleted
            $existingLeave = $this->where('id', $id)->where('is_deleted', false)->first();
            if (!$existingLeave) {
                return [
                    'status' => false,
                    'message' => 'Employee leave record not found'
                ];
            }

            // Validate date range
            if (isset($data['date_from']) && isset($data['date_to'])) {
                if (strtotime($data['date_from']) > strtotime($data['date_to'])) {
                    return [
                        'status' => false,
                        'message' => 'Start date cannot be later than end date'
                    ];
                }
            }

            // Update the record
            if ($this->update($id, $data)) {
                return [
                    'status' => true,
                    'message' => 'Employee leave updated successfully'
                ];
            }

            return [
                'status' => false,
                'message' => 'Failed to update employee leave',
                'errors' => $this->errors()
            ];
        } catch (\Exception $e) {
            log_message('error', '[ERROR] {exception}', ['exception' => $e]);
            return [
                'status' => false,
                'message' => 'An error occurred while updating the employee leave'
            ];
        }
    }
}
